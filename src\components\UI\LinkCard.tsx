'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { LinkWithDetails } from '@/types';
import { 
  StarIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  ArrowTopRightOnSquareIcon,
  EllipsisHorizontalIcon,
  FlagIcon
} from '@heroicons/react/24/outline';
import { 
  StarIcon as StarIconSolid,
  HeartIcon as HeartIconSolid
} from '@heroicons/react/24/solid';
import { formatRelativeTime, extractDomain, truncateText, getInitials } from '@/utils/helpers';
import { ratingService, favoriteService } from '@/firebase/firestore';
import ReportModal from './ReportModal';

interface LinkCardProps {
  link: LinkWithDetails;
  onRatingChange?: (linkId: string, newRating: number) => void;
  onFavoriteChange?: (linkId: string, isFavorited: boolean) => void;
  showCategory?: boolean;
  className?: string;
}

const LinkCard: React.FC<LinkCardProps> = ({ 
  link, 
  onRatingChange,
  onFavoriteChange,
  showCategory = true,
  className = ""
}) => {
  const { user } = useAuth();
  const [isRating, setIsRating] = useState(false);
  const [isFavoriting, setIsFavoriting] = useState(false);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [reportModal, setReportModal] = useState({
    isOpen: false,
    targetId: '',
    targetTitle: ''
  });

  // Handle rating submission
  const handleRating = async (rating: number) => {
    if (!user || isRating) return;

    try {
      setIsRating(true);
      await ratingService.create(link.id, user.id, rating);
      onRatingChange?.(link.id, rating);
    } catch (error) {
      console.error('Failed to rate link:', error);
    } finally {
      setIsRating(false);
    }
  };

  // Handle favorite toggle
  const handleFavorite = async () => {
    if (!user || isFavoriting) return;

    try {
      setIsFavoriting(true);
      
      if (link.isFavorited) {
        await favoriteService.remove(user.id, link.id);
        onFavoriteChange?.(link.id, false);
      } else {
        await favoriteService.add(user.id, link.id);
        onFavoriteChange?.(link.id, true);
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    } finally {
      setIsFavoriting(false);
    }
  };

  // Handle report
  const handleReport = () => {
    setReportModal({
      isOpen: true,
      targetId: link.id,
      targetTitle: link.title
    });
    setShowDropdown(false);
  };

  const closeReportModal = () => {
    setReportModal({
      isOpen: false,
      targetId: '',
      targetTitle: ''
    });
  };

  // Render star rating
  const renderStars = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => {
          const isHovered = hoveredRating >= star;
          const isRated = (link.userRating || 0) >= star;
          const isAverageRated = !user && (link.averageRating || 0) >= star;
          
          return (
            <button
              key={star}
              onMouseEnter={() => setHoveredRating(star)}
              onMouseLeave={() => setHoveredRating(0)}
              onClick={() => handleRating(star)}
              disabled={!user || isRating}
              className={`transition-colors ${
                user ? 'hover:scale-110 cursor-pointer' : 'cursor-default'
              }`}
            >
              {(isHovered || isRated || isAverageRated) ? (
                <StarIconSolid className="h-4 w-4 text-yellow-400" />
              ) : (
                <StarIcon className="h-4 w-4 text-gray-300" />
              )}
            </button>
          );
        })}
        <span className="text-sm text-gray-500 ml-2">
          {link.averageRating ? link.averageRating.toFixed(1) : '0.0'} 
          ({link.totalRatings || 0})
        </span>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-md ${className}`}>
      <div className="p-6">
        {/* Header with category and submitter */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            {showCategory && (
              <Link
                href={`/category/${link.category.slug}`}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
              >
                {link.category.name}
              </Link>
            )}
            
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Link
                href={`/user/${link.submitter.username}`}
                className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
              >
                {link.submitter.avatar ? (
                  <img
                    src={link.submitter.avatar}
                    alt={link.submitter.displayName}
                    className="h-5 w-5 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-5 w-5 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs">
                    {getInitials(link.submitter.displayName)}
                  </div>
                )}
                <span>{link.submitter.displayName}</span>
              </Link>
              <span>•</span>
              <span>{formatRelativeTime(link.submittedAt)}</span>
            </div>
          </div>
          
          <div className="relative">
            <button 
              onClick={() => setShowDropdown(!showDropdown)}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <EllipsisHorizontalIcon className="h-5 w-5 text-gray-400" />
            </button>
            
            {showDropdown && (
              <div className="absolute right-0 top-8 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                {user && (
                  <button
                    onClick={handleReport}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
                  >
                    <FlagIcon className="h-4 w-4 mr-2" />
                    Melden
                  </button>
                )}
                {!user && (
                  <div className="px-3 py-2 text-sm text-gray-500">
                    Anmelden erforderlich
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Title and Description */}
        <div className="mb-4">
          <Link href={`/link/${link.id}`}>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-6 hover:text-blue-600 transition-colors cursor-pointer">
              {link.title}
            </h3>
          </Link>
          {link.description && (
            <p className="text-gray-600 text-sm leading-5">
              {truncateText(link.description, 150)}
            </p>
          )}
        </div>

        {/* URL */}
        <div className="mb-4">
          <a
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors group"
          >
            <span className="text-sm font-medium">{extractDomain(link.url)}</span>
            <ArrowTopRightOnSquareIcon className="h-4 w-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
          </a>
        </div>

        {/* Rating */}
        <div className="mb-4">
          {renderStars()}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Favorite Button */}
            <button
              onClick={handleFavorite}
              disabled={!user || isFavoriting}
              className={`flex items-center space-x-1 transition-colors ${
                user 
                  ? 'hover:text-red-600 cursor-pointer' 
                  : 'cursor-default'
              } ${
                link.isFavorited ? 'text-red-600' : 'text-gray-500'
              }`}
            >
              {link.isFavorited ? (
                <HeartIconSolid className="h-5 w-5" />
              ) : (
                <HeartIcon className="h-5 w-5" />
              )}
              <span className="text-sm">Favorit</span>
            </button>

            {/* Comments */}
            <Link
              href={`/link/${link.id}`}
              className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors"
            >
              <ChatBubbleLeftIcon className="h-5 w-5" />
              <span className="text-sm">{link.totalComments || 0} Kommentare</span>
            </Link>
          </div>

          {/* Featured Badge */}
          {link.isFeatured && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              ⭐ Featured
            </span>
          )}
        </div>
      </div>

      {/* Report Modal */}
      <ReportModal
        isOpen={reportModal.isOpen}
        onClose={closeReportModal}
        targetType="link"
        targetId={reportModal.targetId}
        targetTitle={reportModal.targetTitle}
      />
    </div>
  );
};

export default LinkCard; 