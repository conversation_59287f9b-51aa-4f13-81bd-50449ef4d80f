'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { linkService, categoryService, reportService, userService } from '@/firebase/firestore';
import { LinkWithDetails, Category, Report } from '@/types';
import LinkCard from '@/components/UI/LinkCard';
import AdminLinkCard from '@/components/Admin/AdminLinkCard';
import BulkActions from '@/components/Admin/BulkActions';
import { formatRelativeTime } from '@/utils/helpers';
import {
  ChartBarIcon,
  UserGroupIcon,
  LinkIcon,
  FolderIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FlagIcon
} from '@heroicons/react/24/outline';

export default function AdminPage() {
  const { user } = useAuth();
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'links' | 'categories' | 'users' | 'reports'>('overview');
  const [pendingLinks, setPendingLinks] = useState<LinkWithDetails[]>([]);
  const [allLinks, setAllLinks] = useState<LinkWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLinks, setSelectedLinks] = useState<string[]>([]);
  const [stats, setStats] = useState({
    totalLinks: 0,
    pendingLinks: 0,
    totalCategories: 0,
    totalUsers: 0,
    pendingReports: 0,
    thisWeekSubmissions: 0
  });

  // Category management state
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: ''
  });

  // Recent activities state
  const [recentActivities, setRecentActivities] = useState<Array<{
    id: string;
    type: 'link_approved' | 'link_submitted' | 'category_created' | 'report_submitted';
    title: string;
    timestamp: Date;
    color: string;
  }>>([]);

  // Check if user is admin (you can implement this logic)
  const isAdmin = user?.email === '<EMAIL>';

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    if (!isAdmin) {
      router.push('/');
      return;
    }

    loadAdminData();
  }, [user, isAdmin, router]);

  const loadAdminData = async () => {
    try {
      setLoading(true);
      
      // Load all data in parallel for better performance
      const [
        pendingLinksData,
        allLinksData,
        categoriesData,
        reportsData,
        totalApprovedLinks,
        totalUsers
      ] = await Promise.all([
        linkService.getPending(),
        linkService.getAll({ pageSize: 50 }).then(response => response.data),
        categoryService.getAll(),
        reportService.getPending(),
        linkService.getTotalApprovedCount(),
        userService.getTotalCount()
      ]);

      setPendingLinks(pendingLinksData);
      setAllLinks(allLinksData);
      setCategories(categoriesData);
      setReports(reportsData);

      // Load recent activities
      await loadRecentActivities();
      
      setStats({
        totalLinks: totalApprovedLinks,
        pendingLinks: pendingLinksData.length,
        totalCategories: categoriesData.length,
        totalUsers: totalUsers,
        pendingReports: reportsData.length,
        thisWeekSubmissions: 0 // TODO: implement weekly submissions count
      });
      
    } catch (error) {
      console.error('Fehler beim Laden der Admin-Daten:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentActivities = async () => {
    try {
      const activities: Array<{
        id: string;
        type: 'link_approved' | 'link_submitted' | 'category_created' | 'report_submitted';
        title: string;
        timestamp: Date;
        color: string;
      }> = [];

      // Get recent approved links (last 10)
      const approvedLinks = await linkService.getRecentApproved(5);
      approvedLinks.forEach(link => {
        activities.push({
          id: `link_${link.id}`,
          type: 'link_approved' as const,
          title: `Link "${link.title}" genehmigt`,
          timestamp: link.approvedAt || link.updatedAt,
          color: 'blue'
        });
      });

      // Get recent submitted links (last 5)
      const recentSubmitted = await linkService.getRecentSubmitted(3);
      recentSubmitted.forEach(link => {
        activities.push({
          id: `submitted_${link.id}`,
          type: 'link_submitted' as const,
          title: `Neuer Link "${link.title}" eingereicht`,
          timestamp: link.submittedAt,
          color: 'yellow'
        });
      });

      // Get recent categories (last 3)
      const recentCategories = await categoryService.getRecent(2);
      recentCategories.forEach(category => {
        activities.push({
          id: `category_${category.id}`,
          type: 'category_created' as const,
          title: `Neue Kategorie "${category.name}" erstellt`,
          timestamp: category.createdAt,
          color: 'green'
        });
      });

      // Get recent reports (last 3)
      const recentReports = await reportService.getRecent(2);
      recentReports.forEach(report => {
        activities.push({
          id: `report_${report.id}`,
          type: 'report_submitted' as const,
          title: `${report.targetType === 'link' ? 'Link' : 'Kommentar'} gemeldet`,
          timestamp: report.createdAt,
          color: 'red'
        });
      });

      // Sort by timestamp (newest first) and take top 10
      const sortedActivities = activities
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 10);

      setRecentActivities(sortedActivities);
      
    } catch (error) {
      console.error('Fehler beim Laden der Aktivitäten:', error);
    }
  };

  const handleApproveLink = async (linkId: string) => {
    try {
      await linkService.approve(linkId);
      
      // Remove from pending list
      setPendingLinks(prev => prev.filter(link => link.id !== linkId));
      
      // Update stats
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - 1,
        totalLinks: prev.totalLinks + 1
      }));

      // Reload activities to show the approved link
      await loadRecentActivities();

      // Show success message (optional)
      console.log('Link erfolgreich genehmigt!');
      
    } catch (error) {
      console.error('Fehler beim Genehmigen:', error);
    }
  };

  const handleRejectLink = async (linkId: string) => {
    try {
      await linkService.reject(linkId);
      
      // Remove from pending list
      setPendingLinks(prev => prev.filter(link => link.id !== linkId));
      
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - 1
      }));

      // Show success message (optional)
      console.log('Link erfolgreich abgelehnt!');
      
    } catch (error) {
      console.error('Fehler beim Ablehnen:', error);
    }
  };

  const handleApproveReport = async (reportId: string) => {
    try {
      await reportService.updateStatus(reportId, 'resolved', user!.id, 'Content approved by admin');
      
      // Remove from reports list
      setReports(prev => prev.filter(report => report.id !== reportId));
      
      setStats(prev => ({
        ...prev,
        pendingReports: prev.pendingReports - 1
      }));

      console.log('Meldung erfolgreich genehmigt!');
      
    } catch (error) {
      console.error('Fehler beim Genehmigen der Meldung:', error);
    }
  };

  const handleRemoveReportedContent = async (report: Report) => {
    try {
      if (report.targetType === 'link') {
        await linkService.reject(report.targetId);
      }
      // For comments, we would need a comment deletion function
      // TODO: Implement comment deletion in commentService
      
      await reportService.updateStatus(report.id, 'resolved', user!.id, 'Content removed by admin');
      
      // Remove from reports list
      setReports(prev => prev.filter(r => r.id !== report.id));
      
      setStats(prev => ({
        ...prev,
        pendingReports: prev.pendingReports - 1
      }));

      console.log('Gemeldeter Inhalt erfolgreich entfernt!');
      
    } catch (error) {
      console.error('Fehler beim Entfernen des Inhalts:', error);
    }
  };

  const handleDismissReport = async (reportId: string) => {
    try {
      await reportService.updateStatus(reportId, 'dismissed', user!.id, 'Report dismissed by admin');
      
      // Remove from reports list
      setReports(prev => prev.filter(report => report.id !== reportId));
      
      setStats(prev => ({
        ...prev,
        pendingReports: prev.pendingReports - 1
      }));

      console.log('Meldung erfolgreich verworfen!');
      
    } catch (error) {
      console.error('Fehler beim Verwerfen der Meldung:', error);
    }
  };

  // Category management handlers
  const handleCreateCategory = async () => {
    try {
      if (!categoryForm.name.trim()) {
        alert('Kategorie-Name ist erforderlich');
        return;
      }

      if (!user?.id) {
        alert('Benutzer nicht angemeldet');
        return;
      }

      await categoryService.create(categoryForm, user.id);
      
      // Reload categories
      const categoriesData = await categoryService.getAll();
      setCategories(categoriesData);
      
      // Update stats
      setStats(prev => ({
        ...prev,
        totalCategories: categoriesData.length
      }));

      // Reset form and close modal
      setCategoryForm({ name: '', description: '' });
      setShowCategoryModal(false);
      setEditingCategory(null);

      // Reload activities to show the new category
      await loadRecentActivities();

      console.log('Kategorie erfolgreich erstellt!');
      
    } catch (error) {
      console.error('Fehler beim Erstellen der Kategorie:', error);
      alert('Fehler beim Erstellen der Kategorie');
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setCategoryForm({
      name: category.name,
      description: category.description || ''
    });
    setShowCategoryModal(true);
  };

  const handleUpdateCategory = async () => {
    try {
      if (!editingCategory || !categoryForm.name.trim()) {
        alert('Kategorie-Name ist erforderlich');
        return;
      }

      // Update category in Firestore
      await categoryService.update(editingCategory.id, {
        name: categoryForm.name,
        description: categoryForm.description
      });
      
      // Reload categories
      const categoriesData = await categoryService.getAll();
      setCategories(categoriesData);

      // Reset form and close modal
      setCategoryForm({ name: '', description: '' });
      setShowCategoryModal(false);
      setEditingCategory(null);

      // Reload activities to show the updated category
      await loadRecentActivities();

      console.log('Kategorie erfolgreich aktualisiert!');
      
    } catch (error) {
      console.error('Fehler beim Aktualisieren der Kategorie:', error);
      alert('Fehler beim Aktualisieren der Kategorie');
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm('Bist du sicher, dass du diese Kategorie löschen möchtest?')) {
      return;
    }

    try {
      await categoryService.delete(categoryId);
      
      // Remove from categories list
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));
      
      // Update stats
      setStats(prev => ({
        ...prev,
        totalCategories: prev.totalCategories - 1
      }));

      // Reload activities to remove the deleted category
      await loadRecentActivities();

      console.log('Kategorie erfolgreich gelöscht!');
      
    } catch (error) {
      console.error('Fehler beim Löschen der Kategorie:', error);
      alert('Fehler beim Löschen der Kategorie');
    }
  };

  const closeCategoryModal = () => {
    setShowCategoryModal(false);
    setEditingCategory(null);
    setCategoryForm({ name: '', description: '' });
  };

  // Bulk actions handlers
  const handleSelectLink = (linkId: string, selected: boolean) => {
    setSelectedLinks(prev =>
      selected
        ? [...prev, linkId]
        : prev.filter(id => id !== linkId)
    );
  };

  const handleSelectAll = (links: LinkWithDetails[], selected: boolean) => {
    const linkIds = links.map(link => link.id);
    setSelectedLinks(prev =>
      selected
        ? [...new Set([...prev, ...linkIds])]
        : prev.filter(id => !linkIds.includes(id))
    );
  };

  const handleBulkAction = (action: string, linkIds: string[]) => {
    // Update local state based on action
    if (action === 'approve') {
      setPendingLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
      setAllLinks(prev => prev.map(link =>
        linkIds.includes(link.id) ? { ...link, isApproved: true, approvedAt: new Date() } : link
      ));
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - linkIds.length,
        totalLinks: prev.totalLinks + linkIds.length
      }));
    } else if (action === 'reject') {
      setPendingLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
      setAllLinks(prev => prev.filter(link => !linkIds.includes(link.id)));
      setStats(prev => ({
        ...prev,
        pendingLinks: prev.pendingLinks - linkIds.length
      }));
    } else if (action === 'feature' || action === 'unfeature') {
      const isFeatured = action === 'feature';
      setAllLinks(prev => prev.map(link =>
        linkIds.includes(link.id) ? { ...link, isFeatured } : link
      ));
      setPendingLinks(prev => prev.map(link =>
        linkIds.includes(link.id) ? { ...link, isFeatured } : link
      ));
    }

    // Reload activities
    loadRecentActivities();
  };

  const clearSelection = () => {
    setSelectedLinks([]);
  };

  // Sort categories by name
  const sortedCategories = [...categories].sort((a, b) => a.name.localeCompare(b.name));

  if (!user || !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Zugriff verweigert</h1>
          <p className="text-gray-600">Du hast keine Berechtigung für den Admin-Bereich.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-48 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="mt-2 text-gray-600">Verwalte Links, Kategorien und Benutzer</p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <LinkIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Gesamt Links</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalLinks}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Warteschlange</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pendingLinks}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FolderIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Kategorien</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalCategories}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Benutzer</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FlagIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Meldungen</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pendingReports}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Übersicht
              </button>
              <button
                onClick={() => setActiveTab('links')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'links'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Links verwalten ({pendingLinks.length})
              </button>
              <button
                onClick={() => setActiveTab('categories')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'categories'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Kategorien ({categories.length})
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'users'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Benutzer
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'reports'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Meldungen ({reports.length})
              </button>
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6">
          
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              
              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Schnellaktionen</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <button
                    onClick={() => setActiveTab('links')}
                    className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Links genehmigen</p>
                      <p className="text-sm text-gray-600">{pendingLinks.length} wartend</p>
                    </div>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('categories')}
                    className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <FolderIcon className="h-6 w-6 text-blue-600 mr-3" />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Kategorien verwalten</p>
                      <p className="text-sm text-gray-600">{categories.length} aktiv</p>
                    </div>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('users')}
                    className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <UserGroupIcon className="h-6 w-6 text-purple-600 mr-3" />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Benutzer verwalten</p>
                      <p className="text-sm text-gray-600">Rollen & Rechte</p>
                    </div>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('reports')}
                    className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <FlagIcon className="h-6 w-6 text-red-600 mr-3" />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Meldungen bearbeiten</p>
                      <p className="text-sm text-gray-600">{reports.length} ausstehend</p>
                    </div>
                  </button>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Letzte Aktivitäten</h3>
                <div className="space-y-3">
                  {recentActivities.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">Keine aktuellen Aktivitäten</p>
                  ) : (
                    recentActivities.map((activity) => (
                      <div key={activity.id} className="flex items-center text-sm">
                        <div className={`h-2 w-2 rounded-full mr-3 ${
                          activity.color === 'green' ? 'bg-green-400' :
                          activity.color === 'blue' ? 'bg-blue-400' :
                          activity.color === 'yellow' ? 'bg-yellow-400' :
                          activity.color === 'red' ? 'bg-red-400' :
                          'bg-gray-400'
                        }`}></div>
                        <span className="text-gray-600">{activity.title}</span>
                        <span className="ml-auto text-gray-400">
                          {formatRelativeTime(activity.timestamp)}
                        </span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Links Management Tab */}
          {activeTab === 'links' && (
            <div className="space-y-6">
              {/* Pending Links Section */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Wartende Links ({pendingLinks.length})
                  </h3>
                  {pendingLinks.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSelectAll(pendingLinks, !pendingLinks.every(link => selectedLinks.includes(link.id)))}
                        className="text-sm text-blue-600 hover:text-blue-700"
                      >
                        {pendingLinks.every(link => selectedLinks.includes(link.id)) ? 'Alle abwählen' : 'Alle auswählen'}
                      </button>
                    </div>
                  )}
                </div>

                {/* Bulk Actions for Pending Links */}
                {selectedLinks.some(id => pendingLinks.find(link => link.id === id)) && (
                  <BulkActions
                    selectedLinks={allLinks.filter(link => selectedLinks.includes(link.id))}
                    onAction={handleBulkAction}
                    onClearSelection={clearSelection}
                  />
                )}

                {pendingLinks.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircleIcon className="h-12 w-12 text-green-400 mx-auto mb-4" />
                    <p className="text-gray-600">Alle Links sind bearbeitet!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pendingLinks.map((link) => (
                      <AdminLinkCard
                        key={link.id}
                        link={link}
                        isSelected={selectedLinks.includes(link.id)}
                        onSelect={handleSelectLink}
                        onApprove={handleApproveLink}
                        onReject={handleRejectLink}
                        showActions={true}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* All Links Section */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Alle Links ({allLinks.length})
                  </h3>
                  {allLinks.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSelectAll(allLinks, !allLinks.every(link => selectedLinks.includes(link.id)))}
                        className="text-sm text-blue-600 hover:text-blue-700"
                      >
                        {allLinks.every(link => selectedLinks.includes(link.id)) ? 'Alle abwählen' : 'Alle auswählen'}
                      </button>
                    </div>
                  )}
                </div>

                {/* Bulk Actions for All Links */}
                {selectedLinks.some(id => allLinks.find(link => link.id === id)) && (
                  <BulkActions
                    selectedLinks={allLinks.filter(link => selectedLinks.includes(link.id))}
                    onAction={handleBulkAction}
                    onClearSelection={clearSelection}
                  />
                )}

                {allLinks.length === 0 ? (
                  <div className="text-center py-8">
                    <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Noch keine Links vorhanden</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {allLinks.slice(0, 20).map((link) => (
                      <AdminLinkCard
                        key={link.id}
                        link={link}
                        isSelected={selectedLinks.includes(link.id)}
                        onSelect={handleSelectLink}
                        onFeature={(linkId, featured) => {
                          linkService.feature(linkId, featured).then(() => {
                            handleBulkAction(featured ? 'feature' : 'unfeature', [linkId]);
                          });
                        }}
                        showActions={true}
                      />
                    ))}
                    {allLinks.length > 20 && (
                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500">
                          Zeige die ersten 20 von {allLinks.length} Links
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Categories Management Tab */}
          {activeTab === 'categories' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Kategorien verwalten ({categories.length})
                  </h3>
                  <button 
                    onClick={() => setShowCategoryModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Neue Kategorie
                  </button>
                </div>
                
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Links
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                          Aktionen
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sortedCategories.map((category) => (
                        <tr key={category.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-gray-900">{category.name}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs" title={category.description}>
                              {category.description || 'Keine Beschreibung verfügbar'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {category.totalLinks || 0}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              category.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {category.isActive ? 'Aktiv' : 'Inaktiv'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <button 
                                onClick={() => handleEditCategory(category)}
                                className="inline-flex items-center p-2 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                title="Kategorie bearbeiten"
                                aria-label={`Kategorie "${category.name}" bearbeiten`}
                              >
                                <PencilIcon className="h-5 w-5" />
                              </button>
                              <button 
                                onClick={() => {
                                  if (window.confirm(`Bist du sicher, dass du die Kategorie "${category.name}" löschen möchtest?\n\nDies kann nicht rückgängig gemacht werden.`)) {
                                    handleDeleteCategory(category.id);
                                  }
                                }}
                                className="inline-flex items-center p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                                title="Kategorie löschen"
                                aria-label={`Kategorie "${category.name}" löschen`}
                              >
                                <TrashIcon className="h-5 w-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                      {sortedCategories.length === 0 && (
                        <tr>
                          <td colSpan={4} className="px-6 py-8 text-center">
                            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500">Noch keine Kategorien erstellt</p>
                            <button 
                              onClick={() => setShowCategoryModal(true)}
                              className="mt-2 text-blue-600 hover:text-blue-700 font-medium"
                            >
                              Erste Kategorie erstellen
                            </button>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Users Management Tab */}
          {activeTab === 'users' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Benutzer verwalten
                </h3>
                
                <div className="text-center py-8">
                  <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Benutzerverwaltung wird bald implementiert</p>
                </div>
              </div>
            </div>
          )}

          {/* Reports Management Tab */}
          {activeTab === 'reports' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Gemeldete Inhalte ({reports.length})
                </h3>
                
                {reports.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircleIcon className="h-12 w-12 text-green-400 mx-auto mb-4" />
                    <p className="text-gray-600">Keine ausstehenden Meldungen!</p>
                  </div>
                ) : (
                  <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-300">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Typ
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Grund
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Gemeldet von
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Datum
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Aktionen
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reports.map((report) => (
                          <tr key={report.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                report.targetType === 'link' 
                                  ? 'bg-blue-100 text-blue-800' 
                                  : 'bg-purple-100 text-purple-800'
                              }`}>
                                {report.targetType === 'link' ? 'Link' : 'Kommentar'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {report.reason === 'spam' && 'Spam oder Werbung'}
                                {report.reason === 'inappropriate' && 'Unangemessener Inhalt'}
                                {report.reason === 'harassment' && 'Belästigung oder Mobbing'}
                                {report.reason === 'copyright' && 'Urheberrechtsverletzung'}
                                {report.reason === 'misinformation' && 'Fehlinformation'}
                                {report.reason === 'other' && 'Anderer Grund'}
                              </div>
                              {report.customReason && (
                                <div className="text-sm text-gray-500">{report.customReason}</div>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {report.reportedBy}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(report.createdAt).toLocaleDateString('de-DE')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                report.status === 'pending' 
                                  ? 'bg-yellow-100 text-yellow-800' 
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {report.status === 'pending' ? 'Wartend' : 'Bearbeitet'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex flex-wrap gap-2">
                                <button 
                                  onClick={() => {
                                    if (window.confirm('Meldung als berechtigt markieren?\n\nDer gemeldete Inhalt bleibt online.')) {
                                      handleApproveReport(report.id);
                                    }
                                  }}
                                  className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-green-500"
                                  title="Meldung als berechtigt markieren"
                                >
                                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  Genehmigen
                                </button>
                                <button 
                                  onClick={() => {
                                    if (window.confirm(`Gemeldeten ${report.targetType === 'link' ? 'Link' : 'Kommentar'} entfernen?\n\nDieser Inhalt wird permanent gelöscht.`)) {
                                      handleRemoveReportedContent(report);
                                    }
                                  }}
                                  className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                                  title="Gemeldeten Inhalt entfernen"
                                >
                                  <TrashIcon className="h-3 w-3 mr-1" />
                                  Entfernen
                                </button>
                                <button 
                                  onClick={() => {
                                    if (window.confirm('Meldung verwerfen?\n\nDie Meldung wird als unberechtigt markiert.')) {
                                      handleDismissReport(report.id);
                                    }
                                  }}
                                  className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
                                  title="Meldung verwerfen"
                                >
                                  <XCircleIcon className="h-3 w-3 mr-1" />
                                  Verwerfen
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          )}

        </div>

      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {editingCategory ? 'Kategorie bearbeiten' : 'Neue Kategorie erstellen'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm({ ...categoryForm, name: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="z.B. Technologie, Marketing, Design"
                  maxLength={50}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Maximal 50 Zeichen</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Beschreibung
                </label>
                <textarea
                  value={categoryForm.description}
                  onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Kurze Beschreibung der Kategorie..."
                  maxLength={200}
                />
                <p className="text-xs text-gray-500 mt-1">
                  {categoryForm.description.length}/200 Zeichen
                </p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={closeCategoryModal}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Abbrechen
              </button>
              <button
                onClick={editingCategory ? handleUpdateCategory : handleCreateCategory}
                disabled={!categoryForm.name.trim()}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {editingCategory ? 'Aktualisieren' : 'Erstellen'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 