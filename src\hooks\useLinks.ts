import { useState, useEffect } from 'react';
import { linkService } from '@/firebase/firestore';
import { LinkWithDetails, FilterOptions, PaginatedResponse } from '@/types';

interface UseLinksOptions {
  categoryId?: string;
  userId?: string;
  filters?: FilterOptions;
  pageSize?: number;
}

export const useLinks = ({
  categoryId,
  userId,
  filters = { sortBy: 'newest', timeRange: 'all' },
  pageSize = 20
}: UseLinksOptions = {}) => {
  const [links, setLinks] = useState<LinkWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const fetchLinks = async (reset = true) => {
    try {
      setLoading(true);
      setError(null);

      let response: PaginatedResponse<LinkWithDetails>;

      if (categoryId) {
        response = await linkService.getByCategory(categoryId, pageSize);
      } else if (userId) {
        // TODO: Implement getUserLinks in linkService
        response = { data: [], pagination: { page: 1, limit: pageSize, total: 0, totalPages: 0, hasNext: false, hasPrev: false } };
      } else {
        // TODO: Implement getAll with filters in linkService
        response = { data: [], pagination: { page: 1, limit: pageSize, total: 0, totalPages: 0, hasNext: false, hasPrev: false } };
      }

      if (reset) {
        setLinks(response.data);
      } else {
        setLinks(prev => [...prev, ...response.data]);
      }
      
      setHasMore(response.pagination.hasNext);
      setTotalCount(response.pagination.total);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch links');
      if (reset) setLinks([]);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = async () => {
    if (!hasMore || loading) return;
    await fetchLinks(false);
  };

  const refetch = () => {
    fetchLinks(true);
  };

  // Sort links locally based on filters
  const sortedLinks = [...links].sort((a, b) => {
    switch (filters.sortBy) {
      case 'newest':
        return new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime();
      case 'oldest':
        return new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime();
      case 'rating':
        return (b.averageRating || 0) - (a.averageRating || 0);
      case 'popular':
        return (b.totalComments || 0) - (a.totalComments || 0);
      default:
        return 0;
    }
  });

  useEffect(() => {
    fetchLinks(true);
  }, [categoryId, userId, filters.sortBy, filters.timeRange]);

  return {
    links: sortedLinks,
    loading,
    error,
    hasMore,
    totalCount,
    loadMore,
    refetch
  };
}; 