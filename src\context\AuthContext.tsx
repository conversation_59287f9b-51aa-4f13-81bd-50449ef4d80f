'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import { auth } from '@/firebase/config';
import { userService } from '@/firebase/firestore';
import { User, AuthState } from '@/types';

interface AuthContextType extends AuthState {
  // Auth actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, username: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateUserProfile: (data: { displayName?: string; bio?: string; website?: string }) => Promise<void>;
  
  // Utils
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });

  // Debug: Log Firebase config
  useEffect(() => {
    console.log('🔥 Firebase Config Check:', {
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Set' : 'Missing',
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN ? 'Set' : 'Missing',
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID ? 'Set' : 'Missing',
    });

    // Test Firebase connectivity
    import('@/firebase/config').then((firebase) => {
      console.log('🔥 Firebase initialized successfully');
      console.log('🔥 Auth instance:', firebase.auth);
      console.log('🔥 Firestore instance:', firebase.db);
    }).catch((error) => {
      console.error('🚨 Firebase initialization failed:', error);
    });
  }, []);

  // Helper to update error state
  const setError = (error: string | null) => {
    setAuthState(prev => ({ ...prev, error }));
  };

  // Helper to update loading state
  const setLoading = (loading: boolean) => {
    setAuthState(prev => ({ ...prev, loading }));
  };

  // Load user data from Firestore when Firebase user is available
  const loadUserData = async (firebaseUser: FirebaseUser): Promise<User | null> => {
    try {
      console.log('👤 Loading user data for Firebase user:', firebaseUser.uid);
      const userData = await userService.getById(firebaseUser.uid);
      console.log('👤 User data from Firestore:', userData);
      
      if (!userData) {
        console.log('👤 No user data found, creating new user...');
        // If user data doesn't exist in Firestore, create it
        const newUserData = {
          email: firebaseUser.email!,
          username: firebaseUser.email!.split('@')[0], // fallback username
          displayName: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
          bio: '',
          website: '',
          isVerified: false,
          ...(firebaseUser.photoURL && { avatar: firebaseUser.photoURL })
        };
        
        console.log('👤 Creating user with data:', newUserData);
        await userService.create(newUserData, firebaseUser.uid);
        
        const createdUser = { 
          ...newUserData, 
          id: firebaseUser.uid, 
          createdAt: new Date(), 
          updatedAt: new Date() 
        };
        console.log('👤 User created successfully:', createdUser);
        return createdUser;
      }
      
      return userData;
    } catch (error) {
      console.error('🚨 Error loading user data:', error);
      throw error;
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      setError(null);
      setLoading(true);
      console.log('🔐 Attempting to sign in with:', email);
      
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log('✅ Sign in successful:', userCredential.user.uid);
      
    } catch (error: any) {
      console.error('🚨 Sign in failed:', error);
      
      let errorMessage = 'Failed to sign in';
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'Kein Benutzer mit dieser E-Mail gefunden';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Falsches Passwort';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Ungültige E-Mail-Adresse';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Zu viele Anmeldeversuche. Bitte versuche es später erneut.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Netzwerkfehler. Bitte überprüfe deine Internetverbindung.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (
    email: string,
    password: string,
    username: string,
    displayName: string
  ): Promise<void> => {
    try {
      setError(null);
      setLoading(true);

      // Check if username is already taken
      const usernameExists = await userService.checkUsernameExists(username);
      if (usernameExists) {
        throw new Error('Username is already taken');
      }

      // Create Firebase user
      const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);
      
      // Update Firebase profile
      await updateProfile(firebaseUser, { displayName });

      // Create user document in Firestore
      const userData = {
        email,
        username,
        displayName,
        bio: '',
        website: '',
        isVerified: false
      };

      await userService.create(userData, firebaseUser.uid);
      
    } catch (error: any) {
      setError(error.message || 'Failed to create account');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const logout = async (): Promise<void> => {
    try {
      setError(null);
      await signOut(auth);
      setAuthState({ user: null, loading: false, error: null });
    } catch (error: any) {
      setError(error.message || 'Failed to sign out');
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string): Promise<void> => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      setError(error.message || 'Failed to send password reset email');
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (data: {
    displayName?: string;
    bio?: string;
    website?: string;
  }): Promise<void> => {
    try {
      setError(null);
      
      if (!authState.user) {
        throw new Error('No user logged in');
      }

      // Update Firestore document
      await userService.update(authState.user.id, data);
      
      // Update local state
      setAuthState(prev => ({
        ...prev,
        user: prev.user ? { ...prev.user, ...data, updatedAt: new Date() } : null
      }));

      // Update Firebase profile if displayName changed
      if (data.displayName && auth.currentUser) {
        await updateProfile(auth.currentUser, { displayName: data.displayName });
      }
      
    } catch (error: any) {
      setError(error.message || 'Failed to update profile');
      throw error;
    }
  };

  // Refresh user data from Firestore
  const refreshUser = async (): Promise<void> => {
    try {
      if (!auth.currentUser) return;
      
      const userData = await loadUserData(auth.currentUser);
      setAuthState(prev => ({ ...prev, user: userData }));
    } catch (error: any) {
      setError(error.message || 'Failed to refresh user data');
    }
  };

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      setError(null);

      try {
        if (firebaseUser) {
          // User is signed in
          const userData = await loadUserData(firebaseUser);
          setAuthState({
            user: userData,
            loading: false,
            error: null
          });
        } else {
          // User is signed out
          setAuthState({
            user: null,
            loading: false,
            error: null
          });
        }
      } catch (error: any) {
        setAuthState({
          user: null,
          loading: false,
          error: error.message || 'Authentication error'
        });
      }
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    ...authState,
    signIn,
    signUp,
    logout,
    resetPassword,
    updateUserProfile,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 