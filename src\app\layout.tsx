import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/context/AuthContext";
import Header from "@/components/Layout/Header";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "deeplinked - Kuratiertes Link-Archiv",
  description: "Entdecke, teile und bewerte hochwertige Links zu Lerninhalten, Tools und Ressourcen.",
  keywords: ["links", "lernen", "ressourcen", "tools", "community", "kuratiert"],
  authors: [{ name: "deeplinked Team" }],
  creator: "deeplinked",
  openGraph: {
    type: "website",
    locale: "de_DE",
    url: "https://deeplinked.app",
    title: "deeplinked - Kuratiertes Link-Archiv",
    description: "Entdecke, teile und bewerte hochwertige Links zu Lerninhalten, <PERSON><PERSON> und Ressourcen.",
    siteName: "deeplinked",
  },
  twitter: {
    card: "summary_large_image",
    title: "deeplinked - Kuratiertes Link-Archiv",
    description: "Ent<PERSON>cke, teile und bewerte hochwertige Links zu Lerninhalten, Tools und Ressourcen.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    // Add verification IDs when available
    // google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="de" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <AuthProvider>
          <div className="min-h-full">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <footer className="bg-white border-t border-gray-200 mt-auto">
              <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      © 2024 deeplinked. Alle Rechte vorbehalten.
                    </span>
                  </div>
                  <div className="flex items-center space-x-6">
                    <a href="/privacy" className="text-sm text-gray-500 hover:text-gray-900 transition-colors">
                      Datenschutz
                    </a>
                    <a href="/terms" className="text-sm text-gray-500 hover:text-gray-900 transition-colors">
                      AGB
                    </a>
                    <a href="/contact" className="text-sm text-gray-500 hover:text-gray-900 transition-colors">
                      Kontakt
                    </a>
                  </div>
                </div>
              </div>
            </footer>
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
